package com.trs.spdbapi.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.spdbapi.mappers.TbApiOrderMapper;
import com.trs.spdbapi.mappers.TbApiOrderStatusMapper;
import com.trs.spdbapi.mappers.TbApiPrecheckResultMapper;
import com.trs.spdbapi.pojo.entity.TbApiOrder;
import com.trs.spdbapi.pojo.entity.TbApiOrderStatus;
import com.trs.spdbapi.pojo.entity.TbApiPrecheckResult;
import com.trs.spdbapi.pojo.param.R318AddOrderParam;
import com.trs.spdbapi.pojo.param.R319QueryOrderStatusParam;
import com.trs.spdbapi.pojo.param.R320GetDataParam;
import com.trs.spdbapi.pojo.response.BaseResponse;
import com.trs.spdbapi.pojo.response.R318AddOrderResponse;
import com.trs.spdbapi.pojo.response.R319QueryOrderStatusResponse;
import com.trs.spdbapi.pojo.response.R320GetDataResponse;
import com.trs.spdbapi.service.IPrecheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 名单预检服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class PrecheckServiceImpl implements IPrecheckService {

    @Autowired
    private TbApiOrderMapper orderMapper;

    @Autowired
    private TbApiOrderStatusMapper orderStatusMapper;

    @Autowired
    private TbApiPrecheckResultMapper precheckResultMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<R318AddOrderResponse> addOrder(R318AddOrderParam param) {
        try {
            // 生成订单编号
            String orderNo = generateOrderNo();
            
            // 创建订单记录
            TbApiOrder order = new TbApiOrder();
            order.setOrderNo(orderNo);
            order.setEntNames(JSONUtil.toJsonStr(param.getEntNames()));
            order.setOrderStatus("000"); // 数据准备中
            order.setOrderStatusDesc("数据准备中.....请稍后重试！");
            order.setCreateTime(LocalDateTime.now());
            order.setUpdateTime(LocalDateTime.now());
            order.setStatus(1);
            
            orderMapper.insert(order);
            
            // 创建订单状态记录
            TbApiOrderStatus orderStatus = new TbApiOrderStatus();
            orderStatus.setOrderNo(orderNo);
            orderStatus.setOrderStatus("000");
            orderStatus.setOrderStatusDesc("数据准备中.....请稍后重试！");
            orderStatus.setProcessStartTime(LocalDateTime.now());
            orderStatus.setCreateTime(LocalDateTime.now());
            orderStatus.setUpdateTime(LocalDateTime.now());
            orderStatus.setStatus(1);
            
            orderStatusMapper.insert(orderStatus);
            
            // 异步处理企业名称预检（这里简化处理，实际应该异步执行）
            processEntNames(orderNo, param.getEntNames());
            
            // 返回结果
            R318AddOrderResponse response = new R318AddOrderResponse();
            response.setOrderNo(orderNo);
            
            return BaseResponse.success(Arrays.asList(response));
            
        } catch (Exception e) {
            log.error("新增订单失败", e);
            return BaseResponse.fail("新增订单失败：" + e.getMessage());
        }
    }

    @Override
    public BaseResponse<R319QueryOrderStatusResponse> queryOrderStatus(R319QueryOrderStatusParam param) {
        try {
            // 查询订单状态
            QueryWrapper<TbApiOrderStatus> wrapper = new QueryWrapper<>();
            wrapper.eq("order_no", param.getOrderNo())
                   .eq("status", 1)
                   .orderByDesc("update_time")
                   .last("LIMIT 1");
            
            TbApiOrderStatus orderStatus = orderStatusMapper.selectOne(wrapper);
            
            R319QueryOrderStatusResponse response = new R319QueryOrderStatusResponse();
            
            if (orderStatus == null) {
                response.setOrderStatus("010");
                response.setOrderStatusDesc("该订单不存在，请确认订单号！");
            } else {
                response.setOrderStatus(orderStatus.getOrderStatus());
                response.setOrderStatusDesc(orderStatus.getOrderStatusDesc());
            }
            
            return BaseResponse.success(Arrays.asList(response));
            
        } catch (Exception e) {
            log.error("查询订单状态失败", e);
            R319QueryOrderStatusResponse response = new R319QueryOrderStatusResponse();
            response.setOrderStatus("999");
            response.setOrderStatusDesc("系统异常");
            return BaseResponse.success(Arrays.asList(response));
        }
    }

    @Override
    public BaseResponse<R320GetDataResponse> getData(R320GetDataParam param) {
        try {
            // 先检查订单状态
            QueryWrapper<TbApiOrderStatus> statusWrapper = new QueryWrapper<>();
            statusWrapper.eq("order_no", param.getOrderNo())
                        .eq("status", 1)
                        .orderByDesc("update_time")
                        .last("LIMIT 1");
            
            TbApiOrderStatus orderStatus = orderStatusMapper.selectOne(statusWrapper);
            
            if (orderStatus == null) {
                return BaseResponse.fail("该订单不存在，请确认订单号！");
            }
            
            if (!"888".equals(orderStatus.getOrderStatus())) {
                return BaseResponse.fail("数据尚未准备完毕，请稍后重试！");
            }
            
            // 查询预检结果
            QueryWrapper<TbApiPrecheckResult> wrapper = new QueryWrapper<>();
            wrapper.eq("order_no", param.getOrderNo())
                   .eq("page_index", param.getPageIndex())
                   .eq("status", 1);
            
            List<TbApiPrecheckResult> results = precheckResultMapper.selectList(wrapper);
            
            List<R320GetDataResponse> responseList = new ArrayList<>();
            for (TbApiPrecheckResult result : results) {
                R320GetDataResponse response = new R320GetDataResponse();
                response.setReqEntName(result.getReqEntName());
                response.setEntNameList(JSONUtil.toList(result.getEntNameList(), String.class));
                responseList.add(response);
            }
            
            return BaseResponse.success(responseList);
            
        } catch (Exception e) {
            log.error("获取数据失败", e);
            return BaseResponse.fail("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 生成订单编号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + IdUtil.randomUUID().substring(0, 8);
    }

    /**
     * 处理企业名称预检（模拟异步处理）
     */
    private void processEntNames(String orderNo, List<String> entNames) {
        try {
            // 模拟处理时间
            Thread.sleep(1000);
            
            // 为每个企业名称创建预检结果
            for (int i = 0; i < entNames.size(); i++) {
                String entName = entNames.get(i);
                
                TbApiPrecheckResult result = new TbApiPrecheckResult();
                result.setOrderNo(orderNo);
                result.setReqEntName(entName);
                
                // 模拟联想企业名称（实际应该调用外部服务）
                List<String> suggestedNames = generateSuggestedNames(entName);
                result.setEntNameList(JSONUtil.toJsonStr(suggestedNames));
                result.setPageIndex(1); // 简化处理，都放在第一页
                result.setCreateTime(LocalDateTime.now());
                result.setUpdateTime(LocalDateTime.now());
                result.setStatus(1);
                
                precheckResultMapper.insert(result);
            }
            
            // 更新订单状态为完成
            updateOrderStatus(orderNo, "888", "数据准备完毕！");
            
        } catch (Exception e) {
            log.error("处理企业名称预检失败", e);
            updateOrderStatus(orderNo, "999", "系统异常");
        }
    }

    /**
     * 生成联想企业名称（模拟）
     */
    private List<String> generateSuggestedNames(String entName) {
        List<String> suggested = new ArrayList<>();
        suggested.add(entName); // 原名称
        suggested.add(entName + "有限公司");
        suggested.add(entName + "股份有限公司");
        return suggested;
    }

    /**
     * 更新订单状态
     */
    private void updateOrderStatus(String orderNo, String status, String statusDesc) {
        try {
            // 更新订单表
            QueryWrapper<TbApiOrder> orderWrapper = new QueryWrapper<>();
            orderWrapper.eq("order_no", orderNo);
            
            TbApiOrder order = new TbApiOrder();
            order.setOrderStatus(status);
            order.setOrderStatusDesc(statusDesc);
            order.setUpdateTime(LocalDateTime.now());
            
            orderMapper.update(order, orderWrapper);
            
            // 更新订单状态表
            QueryWrapper<TbApiOrderStatus> statusWrapper = new QueryWrapper<>();
            statusWrapper.eq("order_no", orderNo);
            
            TbApiOrderStatus orderStatus = new TbApiOrderStatus();
            orderStatus.setOrderStatus(status);
            orderStatus.setOrderStatusDesc(statusDesc);
            orderStatus.setProcessEndTime(LocalDateTime.now());
            orderStatus.setUpdateTime(LocalDateTime.now());
            
            orderStatusMapper.update(orderStatus, statusWrapper);
            
        } catch (Exception e) {
            log.error("更新订单状态失败", e);
        }
    }
}
