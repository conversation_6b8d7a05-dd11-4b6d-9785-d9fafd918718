package com.trs.spdbapi.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p>
 * 名单预检服务-新增订单(R318) 参数类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "R318AddOrderParam", description = "名单预检服务-新增订单参数")
public class R318AddOrderParam {

    @NotNull(message = "企业名称不能为空")
    @Size(max = 1000, message = "单笔最多传1000个企业")
    @ApiModelProperty(value = "企业名称(多个)", required = true, notes = "单笔最多传1000个企业，大于分多个订单上传")
    private List<String> entNames;

}
