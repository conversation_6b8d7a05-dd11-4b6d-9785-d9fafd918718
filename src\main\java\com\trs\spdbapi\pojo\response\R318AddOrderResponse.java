package com.trs.spdbapi.pojo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 名单预检服务-新增订单(R318) 响应类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "R318AddOrderResponse", description = "名单预检服务-新增订单响应")
public class R318AddOrderResponse {

    @ApiModelProperty(value = "订单编号", required = true, notes = "订单编号长度：Oracle:varchar2(64)")
    private String orderNo;

}
