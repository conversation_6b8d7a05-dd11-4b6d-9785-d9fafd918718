package com.trs.spdbapi.pojo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 名单预检服务-订单状态查询(R319) 响应类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "R319QueryOrderStatusResponse", description = "名单预检服务-订单状态查询响应")
public class R319QueryOrderStatusResponse {

    @ApiModelProperty(value = "订单返回代码", required = true, notes = "888：数据准备完毕！000：数据准备中.....请稍后重试！010：该订单不存在，请确认订单号！999：系统异常")
    private String orderStatus;

    @ApiModelProperty(value = "返回描述", required = true, notes = "中文描述")
    private String orderStatusDesc;

}
