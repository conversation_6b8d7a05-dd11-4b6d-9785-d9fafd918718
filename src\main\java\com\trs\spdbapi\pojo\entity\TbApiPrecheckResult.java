package com.trs.spdbapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 名单预检服务预检结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@TableName("tb_api_precheck_result")
@ApiModel(value = "TbApiPrecheckResult对象", description = "名单预检服务预检结果表")
public class TbApiPrecheckResult extends Model<TbApiPrecheckResult> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "请求企业名称")
    private String reqEntName;

    @ApiModelProperty(value = "预检结果企业名称列表(JSON格式,最多10个联想企业名称)")
    private String entNameList;

    @ApiModelProperty(value = "页码")
    private Integer pageIndex;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "数据有效性(1 生效 0 失效)")
    private Integer status;

}
