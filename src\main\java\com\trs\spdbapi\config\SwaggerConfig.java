package com.trs.spdbapi.config;

import com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @date ：Created in 2025/7/31 15:48
 * @version: 1.0
 */


@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.trs.spdbapi.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("SPDB API文档")
                .description("SPDB API接口文档")
                .version("1.0")
                .build();
    }

    @Bean
    public Knife4jProperties knife4jProperties() {
        Knife4jProperties properties = new Knife4jProperties();
        properties.setEnable(true); // 启用增强功能

        // 自定义设置
        Knife4jProperties.Setting setting = new Knife4jProperties.Setting();
        setting.setLanguage("zh-CN"); // 设置中文
        setting.setEnableSwaggerModels(true); // 启用SwaggerModels
        setting.setEnableDocumentManage(true); // 启用文档管理
        setting.setSwaggerModelName("实体类列表"); // 自定义实体类列表名称

        properties.setSetting(setting);
        return properties;
    }
}