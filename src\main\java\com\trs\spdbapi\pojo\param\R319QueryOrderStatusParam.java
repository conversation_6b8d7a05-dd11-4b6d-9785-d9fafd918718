package com.trs.spdbapi.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <p>
 * 名单预检服务-订单状态查询(R319) 参数类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "R319QueryOrderStatusParam", description = "名单预检服务-订单状态查询参数")
public class R319QueryOrderStatusParam {

    @NotBlank(message = "订单号不能为空")
    @Size(max = 100, message = "订单号长度不能超过100")
    @ApiModelProperty(value = "订单号", required = true, notes = "订单编号长度：Oracle:varchar2(64)")
    private String orderNo;

}
