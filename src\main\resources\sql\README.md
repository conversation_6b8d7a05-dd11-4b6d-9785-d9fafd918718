# 名单预检服务数据库表DDL

## 概述

本目录包含名单预检服务相关的数据库表DDL语句，支持MySQL和Oracle两种数据库。

## 文件说明

- `precheck_tables.sql` - MySQL版本的DDL语句
- `precheck_tables_oracle.sql` - Oracle版本的DDL语句

## 表结构说明

### 1. tb_api_order (订单表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT/NUMBER(19) | 主键ID，自增 |
| order_no | VARCHAR(100) | 订单编号，唯一 |
| ent_names | TEXT/CLOB | 企业名称列表(JSON格式) |
| order_status | VARCHAR(32) | 订单状态 |
| order_status_desc | VARCHAR(200) | 订单状态描述 |
| create_time | DATETIME/DATE | 创建时间 |
| update_time | DATETIME/DATE | 更新时间 |
| status | TINYINT/NUMBER(1) | 数据有效性 |

**订单状态说明：**
- `000` - 数据准备中
- `888` - 数据准备完毕
- `010` - 订单不存在
- `999` - 系统异常

### 2. tb_api_order_status (订单状态表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT/NUMBER(19) | 主键ID，自增 |
| order_no | VARCHAR(100) | 订单编号 |
| order_status | VARCHAR(32) | 订单状态 |
| order_status_desc | VARCHAR(200) | 订单状态描述 |
| process_start_time | DATETIME/DATE | 处理开始时间 |
| process_end_time | DATETIME/DATE | 处理完成时间 |
| create_time | DATETIME/DATE | 创建时间 |
| update_time | DATETIME/DATE | 更新时间 |
| status | TINYINT/NUMBER(1) | 数据有效性 |

### 3. tb_api_precheck_result (预检结果表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT/NUMBER(19) | 主键ID，自增 |
| order_no | VARCHAR(100) | 订单编号 |
| req_ent_name | VARCHAR(200) | 请求企业名称 |
| ent_name_list | TEXT/CLOB | 预检结果企业名称列表(JSON格式) |
| page_index | INT/NUMBER(10) | 页码 |
| create_time | DATETIME/DATE | 创建时间 |
| update_time | DATETIME/DATE | 更新时间 |
| status | TINYINT/NUMBER(1) | 数据有效性 |

## 使用方法

### MySQL

```bash
# 连接到MySQL数据库
mysql -u username -p database_name

# 执行DDL语句
source /path/to/precheck_tables.sql;
```

### Oracle

```bash
# 连接到Oracle数据库
sqlplus username/password@database

# 执行DDL语句
@/path/to/precheck_tables_oracle.sql
```

## 索引说明

为了提高查询性能，已为以下字段创建了索引：

### tb_api_order
- `uk_order_no` - 订单编号唯一索引
- `idx_order_status` - 订单状态索引
- `idx_create_time` - 创建时间索引

### tb_api_order_status
- `idx_order_no` - 订单编号索引
- `idx_order_status` - 订单状态索引
- `idx_process_time` - 处理时间复合索引

### tb_api_precheck_result
- `idx_order_no` - 订单编号索引
- `idx_page_index` - 页码索引
- `idx_req_ent_name` - 请求企业名称索引
- `idx_create_time` - 创建时间索引

## 注意事项

1. **字符集**：MySQL版本使用utf8mb4字符集，支持emoji等特殊字符
2. **自增ID**：Oracle版本使用序列和触发器实现自增ID
3. **JSON存储**：企业名称列表以JSON格式存储在TEXT/CLOB字段中
4. **分页支持**：预检结果表支持分页查询，每页数据量按3M报文大小切分
5. **软删除**：使用status字段实现软删除，1表示有效，0表示无效

## 测试数据

DDL文件中包含了可选的测试数据插入语句，可以根据需要启用。

## 相关接口

这些表支持以下API接口：
- R318 - 名单预检服务-新增订单
- R319 - 名单预检服务-订单状态查询  
- R320 - 名单预检服务-数据获取
