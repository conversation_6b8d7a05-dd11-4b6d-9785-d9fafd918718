package com.trs.spdbapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 名单预检服务订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@TableName("tb_api_order")
@ApiModel(value = "TbApiOrder对象", description = "名单预检服务订单表")
public class TbApiOrder extends Model<TbApiOrder> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "企业名称列表(JSON格式)")
    private String entNames;

    @ApiModelProperty(value = "订单状态(000:数据准备中,888:数据准备完毕,010:订单不存在,999:系统异常)")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusDesc;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "数据有效性(1 生效 0 失效)")
    private Integer status;

}
