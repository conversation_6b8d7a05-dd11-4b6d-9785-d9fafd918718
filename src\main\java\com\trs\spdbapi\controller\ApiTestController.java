package com.trs.spdbapi.controller;


import com.trs.spdbapi.pojo.param.EntListStrategyParam;
import com.trs.spdbapi.pojo.response.BaseResponse;
import com.trs.spdbapi.service.ITbEntListStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 企业名单监控策略表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@RequestMapping("/api")
@Api(value = "/api", tags = "api接口文档[内部调试]")
public class ApiTestController {

    @Autowired
    private ITbEntListStrategyService entListStrategyService;

    @ApiOperation(value = "名单监控策略变更实时接口[R120]", notes = "名单监控策略变更实时接口")
    @PostMapping(value = "/entStrategy")
    public BaseResponse entStrategy(@Validated @RequestBody EntListStrategyParam dto) {
        entListStrategyService.createOrUpdate(dto);
        return BaseResponse.success();
    }
}

