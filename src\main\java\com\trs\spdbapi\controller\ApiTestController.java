package com.trs.spdbapi.controller;


import com.trs.spdbapi.pojo.param.EntListStrategyParam;
import com.trs.spdbapi.pojo.param.R318AddOrderParam;
import com.trs.spdbapi.pojo.param.R319QueryOrderStatusParam;
import com.trs.spdbapi.pojo.param.R320GetDataParam;
import com.trs.spdbapi.pojo.response.BaseResponse;
import com.trs.spdbapi.pojo.response.R318AddOrderResponse;
import com.trs.spdbapi.pojo.response.R319QueryOrderStatusResponse;
import com.trs.spdbapi.pojo.response.R320GetDataResponse;
import com.trs.spdbapi.service.ITbEntListStrategyService;
import com.trs.spdbapi.service.IPrecheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 企业名单监控策略表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@RequestMapping("/api")
@Api(value = "/api", tags = "api接口文档[内部调试]")
public class ApiTestController {

    @Autowired
    private ITbEntListStrategyService entListStrategyService;

    @Autowired
    private IPrecheckService precheckService;

    @ApiOperation(value = "名单监控策略变更实时接口[R120]", notes = "名单监控策略变更实时接口")
    @PostMapping(value = "/entStrategy")
    public BaseResponse entStrategy(@Validated @RequestBody EntListStrategyParam dto) {
        entListStrategyService.createOrUpdate(dto);
        return BaseResponse.success();
    }

    @ApiOperation(value = "名单预检服务-新增订单[R318]", notes = "名单预检服务-新增订单")
    @PostMapping(value = "/addOrder")
    public BaseResponse<R318AddOrderResponse> addOrder(@Validated @RequestBody R318AddOrderParam param) {
        return precheckService.addOrder(param);
    }

    @ApiOperation(value = "名单预检服务-订单状态查询[R319]", notes = "名单预检服务-订单状态查询")
    @PostMapping(value = "/queryOrderStatus")
    public BaseResponse<R319QueryOrderStatusResponse> queryOrderStatus(@Validated @RequestBody R319QueryOrderStatusParam param) {
        return precheckService.queryOrderStatus(param);
    }

    @ApiOperation(value = "名单预检服务-数据获取[R320]", notes = "名单预检服务-数据获取")
    @PostMapping(value = "/getData")
    public BaseResponse<R320GetDataResponse> getData(@Validated @RequestBody R320GetDataParam param) {
        return precheckService.getData(param);
    }
}

