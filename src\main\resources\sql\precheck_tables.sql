-- 名单预检服务相关表DDL语句
-- 创建时间: 2025-08-05
-- 说明: 用于名单预检服务的三个核心表

-- 1. 名单预检服务订单表
DROP TABLE IF EXISTS `tb_api_order`;
CREATE TABLE `tb_api_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` VARCHAR(100) NOT NULL COMMENT '订单编号',
  `ent_names` TEXT COMMENT '企业名称列表(JSON格式)',
  `order_status` VARCHAR(32) DEFAULT '000' COMMENT '订单状态(000:数据准备中,888:数据准备完毕,010:订单不存在,999:系统异常)',
  `order_status_desc` VARCHAR(200) DEFAULT '数据准备中.....请稍后重试！' COMMENT '订单状态描述',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT DEFAULT 1 COMMENT '数据有效性(1 生效 0 失效)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='名单预检服务订单表';

-- 2. 名单预检服务订单状态表
DROP TABLE IF EXISTS `tb_api_order_status`;
CREATE TABLE `tb_api_order_status` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` VARCHAR(100) NOT NULL COMMENT '订单编号',
  `order_status` VARCHAR(32) DEFAULT '000' COMMENT '订单状态(000:数据准备中,888:数据准备完毕,010:订单不存在,999:系统异常)',
  `order_status_desc` VARCHAR(200) DEFAULT '数据准备中.....请稍后重试！' COMMENT '订单状态描述',
  `process_start_time` DATETIME COMMENT '处理开始时间',
  `process_end_time` DATETIME COMMENT '处理完成时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT DEFAULT 1 COMMENT '数据有效性(1 生效 0 失效)',
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_process_time` (`process_start_time`, `process_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='名单预检服务订单状态表';

-- 3. 名单预检服务预检结果表
DROP TABLE IF EXISTS `tb_api_precheck_result`;
CREATE TABLE `tb_api_precheck_result` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` VARCHAR(100) NOT NULL COMMENT '订单编号',
  `req_ent_name` VARCHAR(200) COMMENT '请求企业名称',
  `ent_name_list` TEXT COMMENT '预检结果企业名称列表(JSON格式,最多10个联想企业名称)',
  `page_index` INT DEFAULT 1 COMMENT '页码',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT DEFAULT 1 COMMENT '数据有效性(1 生效 0 失效)',
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_page_index` (`page_index`),
  KEY `idx_req_ent_name` (`req_ent_name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='名单预检服务预检结果表';

-- 插入测试数据（可选）
-- INSERT INTO `tb_api_order` (`order_no`, `ent_names`, `order_status`, `order_status_desc`) 
-- VALUES ('ORD202508050001', '["测试企业1", "测试企业2"]', '000', '数据准备中.....请稍后重试！');

-- 查看表结构
-- SHOW CREATE TABLE `tb_api_order`;
-- SHOW CREATE TABLE `tb_api_order_status`;
-- SHOW CREATE TABLE `tb_api_precheck_result`;
