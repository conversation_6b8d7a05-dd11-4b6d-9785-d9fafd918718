package com.trs.spdbapi.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 名单预检服务订单状态表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@TableName("tb_api_order_status")
@ApiModel(value = "TbApiOrderStatus对象", description = "名单预检服务订单状态表")
public class TbApiOrderStatus extends Model<TbApiOrderStatus> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态(000:数据准备中,888:数据准备完毕,010:订单不存在,999:系统异常)")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusDesc;

    @ApiModelProperty(value = "处理开始时间")
    private LocalDateTime processStartTime;

    @ApiModelProperty(value = "处理完成时间")
    private LocalDateTime processEndTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "数据有效性(1 生效 0 失效)")
    private Integer status;

}
