package com.trs.spdbapi.pojo.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.trs.spdbapi.pojo.response.BaseEnum.SUCCESS;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseResponse<T> {

    /**
     *响应代码
     */
   private String code;

   /**
    * 响应详情
    */
   private String msg;

   /**
    * 业务数据
    */
   private List<T> data;

   /**
    * 总记录数
    */
   private Integer totalCount;

   /**
    *  总页数
    */
   private Integer totalPage;

   /**
    * 每页返回记录数
    */
   private Integer pageSize;

   /**
    * 当前请求页
    */
   private Integer pageIndex;


   private BaseResponse(String code, String msg) {
      this.code = code;
      this.msg = msg;
   }


   /**
    * 返回基本成功对象
    *
    * @param <T> T 泛型标记
    * @return R
    */
   public static <T> BaseResponse<T> success() {
      return new BaseResponse<>(SUCCESS.getCode(), SUCCESS.getMsg());
   }

   /**
    * 返回成功对象带数据
    *
    * @param data 数据
    * @param <T> T 泛型标记
    * @return R
    */
   public static <T> BaseResponse<T> success(List<T> data) {
      BaseResponse<T> response = new BaseResponse<>(SUCCESS.getCode(), SUCCESS.getMsg());
      response.setData(data);
      return response;
   }

   /**
    * 返回失败对象
    *
    * @param msg 失败消息
    * @param <T> T 泛型标记
    * @return R
    */
   public static <T> BaseResponse<T> fail(String msg) {
      return new BaseResponse<>(BaseEnum.FAIL.getCode(), msg);
   }


}
