package com.trs.spdbapi.pojo.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <p>
 * 名单预检服务-数据获取(R320) 参数类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "R320GetDataParam", description = "名单预检服务-数据获取参数")
public class R320GetDataParam {

    @NotBlank(message = "订单号不能为空")
    @Size(max = 100, message = "订单号长度不能超过100")
    @ApiModelProperty(value = "订单号", required = true, notes = "订单号长度：Oracle:varchar2(64)")
    private String orderNo;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "页码", required = true, notes = "每页返回的数据量：按照报文大小切分：3M")
    private Integer pageIndex;

}
