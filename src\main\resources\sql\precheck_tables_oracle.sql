-- 名单预检服务相关表DDL语句 (Oracle版本)
-- 创建时间: 2025-08-05
-- 说明: 用于名单预检服务的三个核心表

-- 1. 名单预检服务订单表
DROP TABLE tb_api_order CASCADE CONSTRAINTS;
CREATE TABLE tb_api_order (
    id NUMBER(19) NOT NULL,
    order_no VARCHAR2(100) NOT NULL,
    ent_names CLOB,
    order_status VARCHAR2(32) DEFAULT '000',
    order_status_desc VARCHAR2(200) DEFAULT '数据准备中.....请稍后重试！',
    create_time DATE DEFAULT SYSDATE,
    update_time DATE DEFAULT SYSDATE,
    status NUMBER(1) DEFAULT 1,
    CONSTRAINT pk_tb_api_order PRIMARY KEY (id),
    CONSTRAINT uk_order_no UNIQUE (order_no)
);

-- 创建序列
DROP SEQUENCE seq_tb_api_order;
CREATE SEQUENCE seq_tb_api_order
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_tb_api_order_id
    BEFORE INSERT ON tb_api_order
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT seq_tb_api_order.NEXTVAL INTO :NEW.id FROM dual;
    END IF;
END;
/

-- 创建索引
CREATE INDEX idx_order_status ON tb_api_order(order_status);
CREATE INDEX idx_create_time ON tb_api_order(create_time);

-- 添加注释
COMMENT ON TABLE tb_api_order IS '名单预检服务订单表';
COMMENT ON COLUMN tb_api_order.id IS '主键ID';
COMMENT ON COLUMN tb_api_order.order_no IS '订单编号';
COMMENT ON COLUMN tb_api_order.ent_names IS '企业名称列表(JSON格式)';
COMMENT ON COLUMN tb_api_order.order_status IS '订单状态(000:数据准备中,888:数据准备完毕,010:订单不存在,999:系统异常)';
COMMENT ON COLUMN tb_api_order.order_status_desc IS '订单状态描述';
COMMENT ON COLUMN tb_api_order.create_time IS '创建时间';
COMMENT ON COLUMN tb_api_order.update_time IS '更新时间';
COMMENT ON COLUMN tb_api_order.status IS '数据有效性(1 生效 0 失效)';

-- 2. 名单预检服务订单状态表
DROP TABLE tb_api_order_status CASCADE CONSTRAINTS;
CREATE TABLE tb_api_order_status (
    id NUMBER(19) NOT NULL,
    order_no VARCHAR2(100) NOT NULL,
    order_status VARCHAR2(32) DEFAULT '000',
    order_status_desc VARCHAR2(200) DEFAULT '数据准备中.....请稍后重试！',
    process_start_time DATE,
    process_end_time DATE,
    create_time DATE DEFAULT SYSDATE,
    update_time DATE DEFAULT SYSDATE,
    status NUMBER(1) DEFAULT 1,
    CONSTRAINT pk_tb_api_order_status PRIMARY KEY (id)
);

-- 创建序列
DROP SEQUENCE seq_tb_api_order_status;
CREATE SEQUENCE seq_tb_api_order_status
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_tb_api_order_status_id
    BEFORE INSERT ON tb_api_order_status
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT seq_tb_api_order_status.NEXTVAL INTO :NEW.id FROM dual;
    END IF;
END;
/

-- 创建索引
CREATE INDEX idx_order_status_no ON tb_api_order_status(order_no);
CREATE INDEX idx_order_status_status ON tb_api_order_status(order_status);
CREATE INDEX idx_process_time ON tb_api_order_status(process_start_time, process_end_time);

-- 添加注释
COMMENT ON TABLE tb_api_order_status IS '名单预检服务订单状态表';
COMMENT ON COLUMN tb_api_order_status.id IS '主键ID';
COMMENT ON COLUMN tb_api_order_status.order_no IS '订单编号';
COMMENT ON COLUMN tb_api_order_status.order_status IS '订单状态(000:数据准备中,888:数据准备完毕,010:订单不存在,999:系统异常)';
COMMENT ON COLUMN tb_api_order_status.order_status_desc IS '订单状态描述';
COMMENT ON COLUMN tb_api_order_status.process_start_time IS '处理开始时间';
COMMENT ON COLUMN tb_api_order_status.process_end_time IS '处理完成时间';
COMMENT ON COLUMN tb_api_order_status.create_time IS '创建时间';
COMMENT ON COLUMN tb_api_order_status.update_time IS '更新时间';
COMMENT ON COLUMN tb_api_order_status.status IS '数据有效性(1 生效 0 失效)';

-- 3. 名单预检服务预检结果表
DROP TABLE tb_api_precheck_result CASCADE CONSTRAINTS;
CREATE TABLE tb_api_precheck_result (
    id NUMBER(19) NOT NULL,
    order_no VARCHAR2(100) NOT NULL,
    req_ent_name VARCHAR2(200),
    ent_name_list CLOB,
    page_index NUMBER(10) DEFAULT 1,
    create_time DATE DEFAULT SYSDATE,
    update_time DATE DEFAULT SYSDATE,
    status NUMBER(1) DEFAULT 1,
    CONSTRAINT pk_tb_api_precheck_result PRIMARY KEY (id)
);

-- 创建序列
DROP SEQUENCE seq_tb_api_precheck_result;
CREATE SEQUENCE seq_tb_api_precheck_result
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_tb_api_precheck_result_id
    BEFORE INSERT ON tb_api_precheck_result
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT seq_tb_api_precheck_result.NEXTVAL INTO :NEW.id FROM dual;
    END IF;
END;
/

-- 创建索引
CREATE INDEX idx_precheck_order_no ON tb_api_precheck_result(order_no);
CREATE INDEX idx_precheck_page_index ON tb_api_precheck_result(page_index);
CREATE INDEX idx_precheck_req_ent_name ON tb_api_precheck_result(req_ent_name);
CREATE INDEX idx_precheck_create_time ON tb_api_precheck_result(create_time);

-- 添加注释
COMMENT ON TABLE tb_api_precheck_result IS '名单预检服务预检结果表';
COMMENT ON COLUMN tb_api_precheck_result.id IS '主键ID';
COMMENT ON COLUMN tb_api_precheck_result.order_no IS '订单编号';
COMMENT ON COLUMN tb_api_precheck_result.req_ent_name IS '请求企业名称';
COMMENT ON COLUMN tb_api_precheck_result.ent_name_list IS '预检结果企业名称列表(JSON格式,最多10个联想企业名称)';
COMMENT ON COLUMN tb_api_precheck_result.page_index IS '页码';
COMMENT ON COLUMN tb_api_precheck_result.create_time IS '创建时间';
COMMENT ON COLUMN tb_api_precheck_result.update_time IS '更新时间';
COMMENT ON COLUMN tb_api_precheck_result.status IS '数据有效性(1 生效 0 失效)';

-- 插入测试数据（可选）
-- INSERT INTO tb_api_order (order_no, ent_names, order_status, order_status_desc) 
-- VALUES ('ORD202508050001', '["测试企业1", "测试企业2"]', '000', '数据准备中.....请稍后重试！');
-- COMMIT;
