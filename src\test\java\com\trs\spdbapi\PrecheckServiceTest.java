package com.trs.spdbapi;

import com.trs.spdbapi.pojo.param.R318AddOrderParam;
import com.trs.spdbapi.pojo.param.R319QueryOrderStatusParam;
import com.trs.spdbapi.pojo.param.R320GetDataParam;
import com.trs.spdbapi.pojo.response.BaseResponse;
import com.trs.spdbapi.pojo.response.R318AddOrderResponse;
import com.trs.spdbapi.pojo.response.R319QueryOrderStatusResponse;
import com.trs.spdbapi.pojo.response.R320GetDataResponse;
import com.trs.spdbapi.service.IPrecheckService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

@SpringBootTest
public class PrecheckServiceTest {

    @Autowired
    private IPrecheckService precheckService;

    @Test
    public void testAddOrder() {
        R318AddOrderParam param = new R318AddOrderParam();
        param.setEntNames(Arrays.asList("测试企业1", "测试企业2", "测试企业3"));
        
        BaseResponse<R318AddOrderResponse> response = precheckService.addOrder(param);
        System.out.println("新增订单结果: " + response);
        
        if (response.getData() != null && !response.getData().isEmpty()) {
            String orderNo = response.getData().get(0).getOrderNo();
            System.out.println("订单号: " + orderNo);
            
            // 测试查询订单状态
            testQueryOrderStatus(orderNo);
            
            // 等待一段时间后测试数据获取
            try {
                Thread.sleep(2000);
                testGetData(orderNo);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private void testQueryOrderStatus(String orderNo) {
        R319QueryOrderStatusParam param = new R319QueryOrderStatusParam();
        param.setOrderNo(orderNo);
        
        BaseResponse<R319QueryOrderStatusResponse> response = precheckService.queryOrderStatus(param);
        System.out.println("查询订单状态结果: " + response);
    }

    private void testGetData(String orderNo) {
        R320GetDataParam param = new R320GetDataParam();
        param.setOrderNo(orderNo);
        param.setPageIndex(1);
        
        BaseResponse<R320GetDataResponse> response = precheckService.getData(param);
        System.out.println("获取数据结果: " + response);
    }
}
