package com.trs.spdbapi.service;

import com.trs.spdbapi.pojo.param.R318AddOrderParam;
import com.trs.spdbapi.pojo.param.R319QueryOrderStatusParam;
import com.trs.spdbapi.pojo.param.R320GetDataParam;
import com.trs.spdbapi.pojo.response.BaseResponse;
import com.trs.spdbapi.pojo.response.R318AddOrderResponse;
import com.trs.spdbapi.pojo.response.R319QueryOrderStatusResponse;
import com.trs.spdbapi.pojo.response.R320GetDataResponse;

/**
 * <p>
 * 名单预检服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface IPrecheckService {

    /**
     * 名单预检服务-新增订单(R318)
     * @param param 新增订单参数
     * @return 订单编号
     */
    BaseResponse<R318AddOrderResponse> addOrder(R318AddOrderParam param);

    /**
     * 名单预检服务-订单状态查询(R319)
     * @param param 订单状态查询参数
     * @return 订单状态信息
     */
    BaseResponse<R319QueryOrderStatusResponse> queryOrderStatus(R319QueryOrderStatusParam param);

    /**
     * 名单预检服务-数据获取(R320)
     * @param param 数据获取参数
     * @return 预检结果数据
     */
    BaseResponse<R320GetDataResponse> getData(R320GetDataParam param);

}
