package com.trs.spdbapi.pojo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 名单预检服务-数据获取(R320) 响应类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "R320GetDataResponse", description = "名单预检服务-数据获取响应")
public class R320GetDataResponse {

    @ApiModelProperty(value = "请求企业名称", notes = "业务方请求企业名称")
    private String reqEntName;

    @ApiModelProperty(value = "预检结果企业名称", notes = "最多10个联想企业名称")
    private List<String> entNameList;

}
